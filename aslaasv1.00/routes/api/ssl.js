const express = require("express");
const router = express.Router();
const path = require('path');

/************ */

router.get('/pki-validation/:fileName', (req, res) => {
    try {
        const fileName = req.params.fileName;
        const filePath = path.join(__dirname, 'certs', fileName);

        res.sendFile(filePath, (err) => {
            if (err) {
                console.log("SSL file serve error:", err);
                res.status(404).send('File not found');
            }
        })
    } catch (err) {
        console.log("SSL route error:", err);
        res.status(500).send('Server error');
    }
});

module.exports = router;