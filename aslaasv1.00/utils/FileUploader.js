const multer = require('multer');
const path = require('path');

const imageStorage = multer.diskStorage({
    destination: './uploads/images',
    filename: (req, file, cb) => {
        cb(
            null,
            file.fieldname + "-c" + Date.now() + path.extname(file.originalname)
        )
    }
})

const imageUpload = multer({
    storage: imageStorage,
    limits: {
        fileSize: 1024 * 1024 * 10
    },
    fileFilter: (req, file, cb) => {
        if (!file.originalname.toLowerCase().match(/\.(png|jpg|jpeg|bmp|tif|tiff|webp)$/)) {
            // upload only png and jpg format

            return cb(new Error("Please upload a Image with image types"));
        }
        cb(null, true);
    }
})

// SSL certificate validation file storage
const sslStorage = multer.diskStorage({
    destination: './routes/api/certs',
    filename: (req, file, cb) => {
        // Keep the original filename for SSL validation files
        cb(null, file.originalname)
    }
})

const sslUpload = multer({
    storage: sslStorage,
    limits: {
        fileSize: 1024 * 1024 * 5 // 5MB limit for SSL files
    },
    fileFilter: (req, file, cb) => {
        // Allow .txt files for SSL validation
        if (!file.originalname.toLowerCase().match(/\.(txt)$/)) {
            return cb(new Error("Please upload a valid SSL validation file (.txt)"));
        }
        cb(null, true);
    }
})

module.exports = {
    imageUpload,
    sslUpload
}